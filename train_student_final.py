"""
学生模型训练脚本 - 最终版本
基于预训练教师模型进行知识蒸馏，100轮训练
"""
import os
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm
from pathlib import Path
import argparse

from dataset.dezh import DeZhTranslationDataset
from model.distillation_transformer import StudentTransformerModel, DistillationLoss
from config import Config, PathConfig
from utils import (collate_fn, clear_memory, print_training_info, save_model_safely, 
                  load_model_safely, create_optimizer, create_scaler, count_parameters)


def train_student_with_distillation(student_model, teacher_model, train_loader, criterion, optimizer, scaler, config, epoch, writer, global_step):
    """知识蒸馏训练学生模型"""
    student_model.train()
    teacher_model.eval()  # 教师模型保持评估模式
    
    total_loss = 0
    loop = tqdm(train_loader, desc=f"Student Epoch {epoch+1}/{config.STUDENT_EPOCHS}")
    
    for batch_idx, (src, tgt) in enumerate(loop):
        # 梯度累积
        if batch_idx % config.GRADIENT_ACCUMULATION_STEPS == 0:
            optimizer.zero_grad()
        
        # 准备输入和目标
        tgt_input = tgt[:, :-1]
        tgt_output = tgt[:, 1:]
        
        # 教师模型前向传播（不计算梯度）
        with torch.no_grad():
            _, teacher_logits = teacher_model(src, tgt_input)
        
        # 学生模型前向传播
        if config.USE_AMP and scaler is not None:
            try:
                # 尝试新版本API
                with torch.amp.autocast('cuda'):
                    _, student_logits = student_model(src, tgt_input)

                    # 计算蒸馏损失
                    hard_loss, soft_loss, total_loss_batch = criterion(
                        student_logits.reshape(-1, student_logits.size(-1)),
                        teacher_logits.reshape(-1, teacher_logits.size(-1)),
                        tgt_output.reshape(-1)
                    )
                    total_loss_batch = total_loss_batch / config.GRADIENT_ACCUMULATION_STEPS
            except AttributeError:
                # 回退到旧版本API
                with torch.cuda.amp.autocast():
                    _, student_logits = student_model(src, tgt_input)

                    # 计算蒸馏损失
                    hard_loss, soft_loss, total_loss_batch = criterion(
                        student_logits.reshape(-1, student_logits.size(-1)),
                        teacher_logits.reshape(-1, teacher_logits.size(-1)),
                        tgt_output.reshape(-1)
                    )
                    total_loss_batch = total_loss_batch / config.GRADIENT_ACCUMULATION_STEPS
        else:
            _, student_logits = student_model(src, tgt_input)

            # 计算蒸馏损失
            hard_loss, soft_loss, total_loss_batch = criterion(
                student_logits.reshape(-1, student_logits.size(-1)),
                teacher_logits.reshape(-1, teacher_logits.size(-1)),
                tgt_output.reshape(-1)
            )
            total_loss_batch = total_loss_batch / config.GRADIENT_ACCUMULATION_STEPS

        # 反向传播
        if config.USE_AMP and scaler is not None:
            scaler.scale(total_loss_batch).backward()
        else:
            total_loss_batch.backward()
        
        # 梯度累积后更新
        if (batch_idx + 1) % config.GRADIENT_ACCUMULATION_STEPS == 0:
            if config.USE_AMP and scaler is not None:
                scaler.unscale_(optimizer)
                torch.nn.utils.clip_grad_norm_(student_model.parameters(), max_norm=1.0)
                scaler.step(optimizer)
                scaler.update()
            else:
                torch.nn.utils.clip_grad_norm_(student_model.parameters(), max_norm=1.0)
                optimizer.step()
        
        total_loss += total_loss_batch.item() * config.GRADIENT_ACCUMULATION_STEPS
        
        # 记录日志
        if global_step % config.LOG_EVERY == 0:
            writer.add_scalar('Loss/Total', total_loss_batch.item() * config.GRADIENT_ACCUMULATION_STEPS, global_step)
            writer.add_scalar('Loss/Hard', hard_loss.item(), global_step)
            writer.add_scalar('Loss/Soft', soft_loss.item(), global_step)
        
        loop.set_postfix({
            'total': total_loss_batch.item() * config.GRADIENT_ACCUMULATION_STEPS,
            'hard': hard_loss.item(),
            'soft': soft_loss.item()
        })
        global_step += 1
        
        # 定期清理内存
        if batch_idx % 100 == 0:
            clear_memory()
    
    return total_loss / len(train_loader), global_step


def validate_student(student_model, teacher_model, val_loader, criterion, device):
    """验证学生模型性能"""
    student_model.eval()
    teacher_model.eval()
    total_loss = 0
    total_hard_loss = 0
    total_soft_loss = 0
    
    with torch.no_grad():
        for src, tgt in val_loader:
            tgt_input = tgt[:, :-1]
            tgt_output = tgt[:, 1:]
            
            # 教师模型前向传播
            _, teacher_logits = teacher_model(src, tgt_input)
            
            # 学生模型前向传播
            _, student_logits = student_model(src, tgt_input)
            
            # 计算蒸馏损失
            hard_loss, soft_loss, total_loss_batch = criterion(
                student_logits.reshape(-1, student_logits.size(-1)),
                teacher_logits.reshape(-1, teacher_logits.size(-1)),
                tgt_output.reshape(-1)
            )
            
            total_loss += total_loss_batch.item()
            total_hard_loss += hard_loss.item()
            total_soft_loss += soft_loss.item()
    
    return {
        'total': total_loss / len(val_loader),
        'hard': total_hard_loss / len(val_loader),
        'soft': total_soft_loss / len(val_loader)
    }


def train_student(teacher_model_path=None, config_class=Config):
    """训练学生模型"""
    config = config_class()
    device = config.get_device()
    
    print("=" * 60)
    print("学生模型知识蒸馏训练")
    print("=" * 60)
    config.print_config()
    
    # 创建目录
    PathConfig.create_directories()
    
    # 加载教师模型
    if teacher_model_path is None:
        teacher_model_path = PathConfig.TEACHER_BEST_MODEL
    
    if not Path(teacher_model_path).exists():
        print(f"错误：找不到教师模型 {teacher_model_path}")
        print("请先训练教师模型：python train_teacher_final.py")
        return None
    
    print(f"加载教师模型: {teacher_model_path}")
    teacher_model = load_model_safely(teacher_model_path, device)
    if teacher_model is None:
        return None
    
    teacher_model.eval()
    
    # 数据加载（使用压缩数据集）
    import glob
    compressed_data_dir = "data/de-zh1_compressed"
    train_files = sorted(glob.glob(f"{compressed_data_dir}/train/*.txt"))
    val_files = sorted(glob.glob(f"{compressed_data_dir}/val/*.txt"))

    if not train_files:
        print(f"错误：在 {compressed_data_dir}/train/ 中未找到训练文件")
        print("请先运行数据压缩：python compress_dataset.py")
        return None

    if not val_files:
        print(f"警告：在 {compressed_data_dir}/val/ 中未找到验证文件")
        print("将使用训练集的一部分作为验证集")
        val_files = train_files[-1:]  # 使用最后一个训练文件作为验证集
        train_files = train_files[:-1]  # 从训练集中移除验证文件

    # 使用前几个文件进行学生模型训练（减少训练时间）
    selected_files = train_files[:4]  # 使用前4个文件（压缩后文件更少）
    print(f"使用 {len(selected_files)} 个压缩文件进行学生模型训练")
    print(f"使用 {len(val_files)} 个文件进行验证")
    
    # 合并选定的文件数据
    all_lines = []
    for file_path in selected_files:
        with open(file_path, 'r', encoding='utf-8') as f:
            all_lines.extend(f.readlines())
    
    # 创建临时数据文件
    temp_data_file = "temp_student_data.txt"
    with open(temp_data_file, 'w', encoding='utf-8') as f:
        f.writelines(all_lines)
    
    # 加载训练数据
    train_dataset = DeZhTranslationDataset(temp_data_file)
    train_loader = DataLoader(
        train_dataset, 
        batch_size=config.BATCH_SIZE, 
        shuffle=True, 
        collate_fn=lambda batch: collate_fn(batch, config.MAX_SEQ_LENGTH, device)
    )
    
    # 加载验证数据
    val_dataset = DeZhTranslationDataset(val_files[0])
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.BATCH_SIZE,
        shuffle=False,
        collate_fn=lambda batch: collate_fn(batch, config.MAX_SEQ_LENGTH, device)
    )
    
    # 创建学生模型
    student_model = StudentTransformerModel(
        d_model=config.STUDENT_DIM,
        src_vocab=train_dataset.de_vocab,
        tgt_vocab=train_dataset.zh_vocab,
        max_seq_length=config.MAX_SEQ_LENGTH,
        device=device,
        num_layers=config.STUDENT_LAYERS,
        num_heads=config.STUDENT_HEADS
    ).to(device)
    
    # 打印模型信息
    teacher_params = count_parameters(teacher_model)
    student_params = count_parameters(student_model)
    compression_ratio = teacher_params / student_params
    
    print(f"\n模型对比:")
    print(f"  教师模型参数: {teacher_params:,}")
    print(f"  学生模型参数: {student_params:,}")
    print(f"  参数压缩比: {compression_ratio:.2f}x")
    
    print_training_info("学生", student_model, len(train_dataset), config.BATCH_SIZE, config.STUDENT_EPOCHS)
    
    # 训练设置
    criterion = DistillationLoss(alpha=config.ALPHA, temperature=config.TEMPERATURE)
    optimizer = create_optimizer(student_model, config.LEARNING_RATE)
    scaler = create_scaler(config.USE_AMP)
    writer = SummaryWriter(config.STUDENT_OUTPUT_DIR + "/logs")
    
    best_val_loss = float('inf')
    best_train_loss = float('inf')
    global_step = 0
    no_improve_epochs = 0
    
    for epoch in range(config.STUDENT_EPOCHS):
        print(f"\n开始第 {epoch+1}/{config.STUDENT_EPOCHS} 轮训练")
        
        # 训练阶段
        epoch_loss, global_step = train_student_with_distillation(
            student_model, teacher_model, train_loader, criterion, optimizer, scaler,
            config, epoch, writer, global_step
        )
        
        print(f"学生模型 Epoch {epoch+1} 训练损失: {epoch_loss:.4f}")
        
        # 验证阶段
        val_metrics = validate_student(student_model, teacher_model, val_loader, criterion, device)
        print(f"学生模型 Epoch {epoch+1} 验证损失:")
        print(f"  总损失: {val_metrics['total']:.4f}")
        print(f"  硬损失: {val_metrics['hard']:.4f}")
        print(f"  软损失: {val_metrics['soft']:.4f}")
        
        # 记录验证指标
        writer.add_scalar('Validation/Total', val_metrics['total'], epoch)
        writer.add_scalar('Validation/Hard', val_metrics['hard'], epoch)
        writer.add_scalar('Validation/Soft', val_metrics['soft'], epoch)
        
        # 保存最佳模型（基于验证损失）
        if val_metrics['total'] < best_val_loss:
            model_path = Path(config.STUDENT_OUTPUT_DIR) / "checkpoints" / "student_best_val.pt"
            if save_model_safely(student_model, model_path):
                best_val_loss = val_metrics['total']
                print(f"新的最佳验证模型已保存，验证损失: {best_val_loss:.4f}")
                no_improve_epochs = 0
        else:
            no_improve_epochs += 1
        
        # 保存最佳训练模型
        if epoch_loss < best_train_loss:
            model_path = Path(config.STUDENT_OUTPUT_DIR) / "checkpoints" / "student_best_train.pt"
            if save_model_safely(student_model, model_path):
                best_train_loss = epoch_loss
                print(f"新的最佳训练模型已保存，训练损失: {best_train_loss:.4f}")
        
        # 定期保存检查点
        if (epoch + 1) % config.SAVE_EVERY == 0:
            checkpoint_path = Path(config.STUDENT_OUTPUT_DIR) / "checkpoints" / f"student_epoch_{epoch+1}.pt"
            save_model_safely(student_model, checkpoint_path)
            print(f"检查点已保存: student_epoch_{epoch+1}.pt")
        
        # 早停检查
        if no_improve_epochs >= config.EARLY_STOPPING_PATIENCE:
            print(f"\n验证损失在 {config.EARLY_STOPPING_PATIENCE} 轮内未改善，停止训练")
            break
    
    writer.close()
    
    # 清理临时文件
    if os.path.exists(temp_data_file):
        os.remove(temp_data_file)
    
    print("\n学生模型训练完成！")
    print(f"最佳验证模型: {config.STUDENT_OUTPUT_DIR}/checkpoints/student_best_val.pt")
    print(f"最佳训练模型: {config.STUDENT_OUTPUT_DIR}/checkpoints/student_best_train.pt")
    
    return student_model


def main():
    parser = argparse.ArgumentParser(description='学生模型训练')
    parser.add_argument('--teacher_model', help='教师模型路径')
    parser.add_argument('--config', choices=['default', '4gb', 'compressed', 'quick'], default='4gb',
                       help='配置类型: default=标准配置, 4gb=4GB显存优化, compressed=压缩数据集, quick=快速测试')

    args = parser.parse_args()

    # 选择配置
    if args.config == '4gb':
        from config import Config4GB
        config_class = Config4GB
    elif args.config == 'compressed':
        from config import Config4GBCompressed
        config_class = Config4GBCompressed
    elif args.config == 'quick':
        from config import ConfigQuick
        config_class = ConfigQuick
    else:
        config_class = Config

    print(f"使用配置: {args.config}")

    # 开始训练
    train_student(args.teacher_model, config_class)


if __name__ == "__main__":
    main()
